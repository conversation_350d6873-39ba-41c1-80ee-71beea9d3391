package com.facishare.open.outer.oa.connector.web.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.admin.StandardConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SystemParams;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.JsapiSignature;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.open.outer.oa.connector.web.openconnetor.OpenConnectorManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Component("standardOAConnectManager")
public class OpenConnectorSettingManager implements OuterAbstractSettingService<StandardConnectorVo> {

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OpenConnectorDataManager openConnectorDataManager;
    @Autowired
    private OpenConnectorManager openConnectorManager;
    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult outerOAConnectSettingResult, ChannelEnum channel,
                                                OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        log.info("doValidateConfigAndSave start: outerOAConnectSettingResult={}, channel={}, outerOaAppInfoTypeEnum={}",
                outerOAConnectSettingResult, channel, outerOaAppInfoTypeEnum);

        if (outerOAConnectSettingResult == null) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        StandardConnectorVo connectorVo = outerOAConnectSettingResult.getConnectParams().getStandard_oa();

        String dataCenterId = outerOAConnectSettingResult.getCurrentDcId();
        connectorVo.setDataCenterId(dataCenterId);
        if (dataCenterId == null) {
            log.error("doValidateConfigAndSave failed: dataCenterId is null");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        if (StringUtils.isEmpty(connectorVo.getOutEa())) {
            log.error("doValidateConfigAndSave failed: outEa is null");
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }

        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        String fsEa = entityById.getFsEa();
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        if(tenantId==null){
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        //校验\处理systemParams
        Result<SystemParams> systemParamsResult = openConnectorManager.processUserInputSystemParams(tenantId.toString(), dataCenterId, connectorVo.getApiName(), connectorVo.getSystemParams());
        if(!systemParamsResult.isSuccess()){
            return Result.copy(systemParamsResult);
        }
        connectorVo.setSystemParams(systemParamsResult.getData());
        connectorVo.setIsBind(true);//系统参数预处理通过

        entityById.setOutEa(connectorVo.getOutEa());
        entityById.setAppId(connectorVo.getAppId());
        entityById.setConnectInfo(JSONObject.toJSONString(connectorVo));
        entityById.setBindType(BindTypeEnum.manual);
        entityById.setBindStatus(BindStatusEnum.normal);
        entityById.setUpdateTime(System.currentTimeMillis());

        Integer count = outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(entityById));
        log.info("doValidateConfigAndSave: count={}", count);
        return Result.newSuccess();
    }

    @Override
    public Result<String> doGetAuthUrl(String connectInfo, ChannelEnum channel,
                                       OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        //目前只有飞书
        return new Result<>();
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {//目前固定
        return Arrays.asList(AlertTypeEnum.CRM_TODO, AlertTypeEnum.CRM_NOTIFICATION);
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName,
                                               String queryFieldValue) {
        return new Result<>();
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {
        OuterOaEnterpriseBindEntity bind = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(bind)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        StandardConnectorVo connectorVo = bind.getConnectParams().getStandard_oa();
        if(StringUtils.isBlank(connectorVo.getOutEa())){
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        String fsEa=bind.getFsEa(),connectorApiName=connectorVo.getApiName(),outEa=bind.getOutEa(),appId=bind.getAppId();
        Integer tenantId=eieaConverter.enterpriseAccountToId(fsEa);
        if(tenantId==null){
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        // 创建新的异步任务
        CompletableFuture<Result<Void>> future = CompletableFuture.supplyAsync(() -> {
            try {
                return openConnectorDataManager.refreshOuterEmpData(bind, tenantId,false);

            } catch (Exception e) {
                log.error("Error processing async task for dataCenterId: {}", dataCenterId, e);
                return Result.newError(ResultCodeEnum.SYS_ERROR.getCode(), "Async processing error");
            }
        });


        // 等待10秒看是否完成
        try {
            return future.get(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.info("Task is still processing for dataCenterId: {}", dataCenterId);
            return Result.newError(ResultCodeEnum.TASK_IS_PROCESSING);
        } catch (Exception e) {
            log.error("Error waiting for task completion for dataCenterId: {}", dataCenterId, e);
            return Result.newError(ResultCodeEnum.SYS_ERROR);
        }
    }


    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        return new Result<>(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        OuterOaEnterpriseBindEntity entityById = outerOaEnterpriseBindManager.getEntityById(dataCenterId);
        if (ObjectUtils.isEmpty(entityById)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        entityById.setOutEa(null);
        entityById.setAppId(null);
        entityById.setBindStatus(BindStatusEnum.create);
        entityById.setUpdateTime(System.currentTimeMillis());
        QywxConnectorVo qywxConnectorVo = JSON.parseObject(entityById.getConnectInfo(), QywxConnectorVo.class);
        qywxConnectorVo.setQywxDepartmentId(null);
        qywxConnectorVo.setQywxEnterpriseName(null);
        qywxConnectorVo.setQywxCorpId(null);
        qywxConnectorVo.setAgentId(null);
        qywxConnectorVo.setAlertConfig(false);
        qywxConnectorVo.setAlertTypes(new ArrayList<>());
        entityById.setConnectInfo(JSONObject.toJSONString(qywxConnectorVo));
        Integer count = outerOaEnterpriseBindManager.batchUpsertById(Lists.newArrayList(entityById));
        log.info("unbindConnect: outerOaEnterpriseBind batchUpsertById count={}", count);
        return new Result<>(Boolean.TRUE);
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        return null;
    }

    @Override
    public Result<JsapiSignature> getJsapiSignature(String appId, String fsEa, String outEa, String url) {
        return null;
    }
}
