package com.facishare.open.outer.oa.connector.common.api.outerInterface.impl;

import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EmplyeeBindChangeTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.JsapiSignature;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 默认实现类，处理未知渠道或不需要特殊处理的渠道
 */
@Component("defaultOuterOASetting")
public class DefaultOuterOASetting implements OuterAbstractSettingService<BaseConnectorVo> {

    private final ChannelEnum channel = ChannelEnum.dingding;

    @Override
    public Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult connectorVo, ChannelEnum channel,
                                                OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        return null;
    }

    @Override
    public Result<String> doGetAuthUrl(String dataCenterId, ChannelEnum channel,
            OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum) {
        return Result.newSuccess("");
    }

    @Override
    public List<AlertTypeEnum> getChannelSupportedAlertTypes() {
        return Collections.emptyList();
    }

    @Override
    public Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName,
            String queryFieldValue) {
        return null;
    }

    @Override
    public Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel) {
        return null;
    }

    @Override
    public Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel) {
        return null;
    }

    @Override
    public Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel) {
        return null;
    }

    @Override
    public Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type) {
        return null;
    }

    @Override
    public Result<JsapiSignature> getJsapiSignature(String appId, String fsEa, String outEa, String url) {
        return null;
    }
}