package com.facishare.open.outer.oa.connector.common.api.outerInterface;

import com.facishare.open.outer.oa.connector.common.api.admin.BaseConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.AlertTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.EmplyeeBindChangeTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.login.CrmUserModel;
import com.facishare.open.outer.oa.connector.common.api.result.JsapiSignature;
import com.facishare.open.outer.oa.connector.common.api.result.OuterOAConnectSettingResult;
import com.facishare.open.outer.oa.connector.common.api.result.Result;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部OA设置服务接口 用于实现不同应用（钉钉、企微、飞书等）的连接器配置、授权和推送功能
 * 
 * @param <T> 连接器配置类型，必须继承自BaseConnectorVo
 */
public interface OuterAbstractSettingService<T extends BaseConnectorVo> {
        /**
         * 执行渠道特定的配置验证
         * 
         * @param connectorVo            连接器配置
         * @param channel                渠道类型
         * @param outerOaAppInfoTypeEnum 外部OA应用信息类型
         * @return 验证结果
         */
        Result<Void> doValidateConfigAndSave(OuterOAConnectSettingResult connectorVo, ChannelEnum channel,
                                             OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum);

        /**
         * 生成渠道特定的授权URL
         * 
         * @param connectInfo            连接器配置
         * @param channel                渠道类型
         * @param outerOaAppInfoTypeEnum 外部OA应用信息类型
         * @return 授权URL
         */
        Result<String> doGetAuthUrl(String dataCenterId, ChannelEnum channel, OuterOaAppInfoTypeEnum outerOaAppInfoTypeEnum);

        /**
         * 获取渠道支持的提醒类型 每个渠道实现类必须指定自己支持的提醒类型列表
         * 
         * @return 该渠道支持的提醒类型列表
         */
        List<AlertTypeEnum> getChannelSupportedAlertTypes();

        /**
         * 根据查询条件获取外部OA员工id
         * 
         * @param dataCenterId      数据中心ID
         * @param channel           渠道类型
         * @param queryFieldApiName 查询字段API名称
         * @param queryFieldValue   查询字段值
         * @return 员工ID
         */
        Result<String> queryOuterEmpByValue(String dataCenterId, ChannelEnum channel, String queryFieldApiName,
                        String queryFieldValue);

        /**
         * 重新触发外部OA员工数据同步
         *
         *
         * @param dataCenterId 数据中心ID
         * @param channel      渠道类型
         * @return 结果
         */
        Result<Void> refreshOuterEmpData(String dataCenterId, ChannelEnum channel);

        /**
         * 根据企业返回是不是路由到新服务了
         *
         *
         * @param tenantId 数据中心ID
         * @param channel      渠道类型
         * @return 结果
         */
        Result<Boolean> routeNewPage(String tenantId, ChannelEnum channel);

        /**
         * 解除绑定，现在只有飞书再用
         */
        Result<Boolean> unbindConnect(String dataCenterId, ChannelEnum channel);

//        /**
//         * 提供给俊文app-view 调用的接口，用于获取飞书ticket->员工信息
//         */
//
//        Result<CrmUserModel> getUserInfoByTicket(String ticket, ChannelEnum channel);

        /**
         * 临时只有企微有用
         */
        Result<Void> employeeBindChangeEvent(String dataCenterId, List<String> ids, EmplyeeBindChangeTypeEnum type);

        /**
         * 提供签名接口
         */
        Result<JsapiSignature> getJsapiSignature(String appId, String fsEa, String outEa, String url);
}
