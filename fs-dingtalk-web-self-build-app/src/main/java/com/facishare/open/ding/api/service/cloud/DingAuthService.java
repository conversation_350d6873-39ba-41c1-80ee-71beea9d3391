package com.facishare.open.ding.api.service.cloud;

import com.facishare.open.ding.api.arg.DingOutSendMessageArg;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/18 18:51 提供给北京换取身份
 * @Version 1.0
 */
public interface DingAuthService {
    Result<DingMappingEmployeeResult> queryEmpById(String dingCorpId, String dingEmpId);

    /**
     *
     * @param url 前端鉴权URL
     * @param enterPriseAccount ea
     * @param appId 112环境70480  线上环境 71075
     * @return
     */
    Result<DingAppResult>  createJsApiSignature(String url, String enterPriseAccount, Long appId);

    Result<DingAppResult>  createJsApiSignatureNew(String url, String enterPriseAccount, String appId);

    /**
     * 通过ea和员工账号进行批量查询员工信息
     *
     * @param ea       ea
     * @param fsEmpIds 批量员工账号
     * @param appId
     * @return
     */
    Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByFsIds(String ea, List<Integer> fsEmpIds, String appId);

/**
 * 个人授权换取身份 https://developers.dingtalk.com/document/app/obtain-user-token?spm=ding_open_doc.document.0.0.2f6e6573aGY4G5
 */
    /**
     *
     * @param authCode 个人授权code
     * @param serviceCorpId  服务窗corpId
     * @param suiteId  应用suiteId
     * @return
     */
    Result<DingPersonResult> queryUserInfoByAuth(String authCode, String serviceCorpId, String suiteId);

    /**
     * 免登获取code
     * @param code 免登code
     * @param appId 第三方应用ID
     * @param corpId 钉钉企业ID
     * @return
     */
    Result<DingOutUserResult> queryUserByCode(String code, String appId, String corpId);

    /**
     * 获取根级部门的人员
     */
    Result<List<EmployeeDingVo>> queryRootUser(String dingCorpId, String suiteId);

    /**
     *  模拟创建订单
     */
    Result<Void> mockCreateOrder(String data,String corpId);

    /**
     * 获取免登token
     * @param dingCorpId 企业corpId
     * @param suiteId 应用suiteId
     */

    Result<String> queryAccessToken(String dingCorpId,String suiteId);

    /**
     * 发送模板消息
     */
    Result<String> sendMessage(DingOutSendMessageArg dingOutSendMessageArg);

    /**
     * dingcorpId获取EA
     */
    Result<String> dingCorpIDtoEA(String dingCorpId);
    /**
     * ea获取dingCorpId
     */
    Result<String> EAtoDingCorpId(String ea);

    /**
     * dingcorpId换取企业信息
     */
    Result<DingCorpMappingVo> queryEnterpriseByCorpId(String dingCorpId);
    /**
     * EA 换取企业信息
     */
    Result<DingCorpMappingVo> queryEnterpriseByEA(String ea);
    /**
     * 根据dingCorpId获取最新的订单信息
     */
    Result<DingOrderResult> queryLastOrder(String dingCorpId);

    /**
     * 纷享员工批量换取钉钉员工的账号
     */
    Result<List<DingMappingEmployeeResult>> batchGetDingEmps(String ea,List<Integer> empIds);

    /**
     * 第三方个人小程序免登code获取员工数据
     */
    Result<EmployeeDingVo> getPersonalData(String appId,String tmpAuthCode);

    /**
     * 临时授权code获取手机号
     * @param authCode 临时授权码
     * @param appId 第三方小程序appId
     */
    Result<EmployeeDingVo> queryUserInfoByMe(String authCode,String appId);


}
