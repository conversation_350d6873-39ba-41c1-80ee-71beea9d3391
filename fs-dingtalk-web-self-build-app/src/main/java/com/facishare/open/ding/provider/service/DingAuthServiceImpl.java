package com.facishare.open.ding.provider.service;

import com.facishare.open.ding.api.arg.DingOutSendMessageArg;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.cloud.DingAuthService;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.manager.TokenManager;
import com.facishare.open.ding.provider.utils.JSApiUtils;
import com.facishare.open.ding.provider.utils.TraceUtils;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2021/6/3 12:50 俊文那边不区分环境，纷享云环境需要部署bubbo接口，返回空级。钉钉云环境有正式的接口数据 主要是提供jsapi的参数
 * @Version 1.0
 */
@Service("dingAuthServiceImpl")
@Slf4j
public class DingAuthServiceImpl implements DingAuthService {
    @Autowired
    private EnterpriseService enterpriseService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private TokenManager tokenManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Override
    public Result<DingMappingEmployeeResult> queryEmpById(String dingCorpId, String dingEmpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<DingAppResult> createJsApiSignature(String url, String enterPriseAccount, Long appId) {
        String traceId = TraceUtils.getTraceId();
        if(StringUtils.isEmpty(traceId)) {
            traceId = UUID.randomUUID().toString();
            TraceUtils.initTraceId(traceId);
        }
        log.info("createJsApiSignature,ea={},appId={},url={}", enterPriseAccount, appId, url);
        //通过自建应用创建createApiSignature
        // 自建应用使用的appId是String,不是入参传的这个,所以只能按企业查出来后取第一个
        final List<OuterOaEnterpriseBindEntity> entitiesByFsEa = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.dingding, enterPriseAccount);
        if (ObjectUtils.isEmpty(entitiesByFsEa)) {
            log.info("createJsApiSignature,FS_CLOUD_NOT_DATA");
            return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
        }
        final OuterOaEnterpriseBindEntity entity = entitiesByFsEa.get(0);
        String appKey = entity.getAppId();
        log.info("createJsApiSignature,enterprise={}", entity);
        String ticket = tokenManager.getTicket(enterPriseAccount, appKey);
        if (ObjectUtils.isEmpty(ticket)) {
            log.info("createJsApiSignature,ticket is empty");
            return Result.newError(ResultCode.FS_CLOUD_CREATE_API_SIGNATURE);
        }
        DingConnectorVo connectInfo = new Gson().fromJson(entity.getConnectInfo(), DingConnectorVo.class);
        Long agentId = Long.valueOf(connectInfo.getAgentId());
        Long timeStamp = new Date().getTime();
        String signValue = "";
        try {
            signValue = JSApiUtils.sign(ticket, "Re6y5aksSpZaCRUwC", timeStamp, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingAppResult dingAppResult = DingAppResult.builder().agentId(agentId).nonceStr("Re6y5aksSpZaCRUwC").corpId(connectInfo.getDingCorpId()).signature(signValue).timeStamp(timeStamp).build();
        log.info("createJsApiSignature,dingAppResult={}", dingAppResult);
        return Result.newSuccess(dingAppResult);
    }

    @Override
    public Result<DingAppResult> createJsApiSignatureNew(String url, String enterPriseAccount, String appId) {
        String appKey = entity.getAppId();
        log.info("createJsApiSignature,enterprise={}", entity);
        String ticket = tokenManager.getTicket(enterPriseAccount, appKey);
        if (ObjectUtils.isEmpty(ticket)) {
            log.info("createJsApiSignature,ticket is empty");
            return Result.newError(ResultCode.FS_CLOUD_CREATE_API_SIGNATURE);
        }
        DingConnectorVo connectInfo = new Gson().fromJson(entity.getConnectInfo(), DingConnectorVo.class);
        Long agentId = Long.valueOf(connectInfo.getAgentId());
        Long timeStamp = new Date().getTime();
        String signValue = "";
        try {
            signValue = JSApiUtils.sign(ticket, "Re6y5aksSpZaCRUwC", timeStamp, url);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DingAppResult dingAppResult = DingAppResult.builder().agentId(agentId).nonceStr("Re6y5aksSpZaCRUwC").corpId(connectInfo.getDingCorpId()).signature(signValue).timeStamp(timeStamp).build();
        log.info("createJsApiSignature,dingAppResult={}", dingAppResult);
        return null;
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetDingEmployeesByFsIds(String ea, List<Integer> fsEmpIds, String appId) {
        Result<DingEnterpriseResult> corpResult = enterpriseService.queryEnterpriseByEa(ea, appId);
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        Integer ei = corpResult.getData().getEi();
        Result<List<DingMappingEmployeeResult>> crmResult = objectMappingService.batchGetDingEmployeesByFsIds(ei, fsEmpIds, appId);
        if (ObjectUtils.isEmpty(crmResult.getData())) {
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        return crmResult;
    }

    @Override
    public Result<DingPersonResult> queryUserInfoByAuth(String authCode, String serviceCorpId, String suiteId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<DingOutUserResult> queryUserByCode(String code, String appId, String corpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<List<EmployeeDingVo>> queryRootUser(String dingCorpId, String suiteId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<Void> mockCreateOrder(String data, String corpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<String> queryAccessToken(String dingCorpId, String suiteId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<String> sendMessage(DingOutSendMessageArg dingOutSendMessageArg) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<String> dingCorpIDtoEA(String dingCorpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<String> EAtoDingCorpId(String ea) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<DingCorpMappingVo> queryEnterpriseByCorpId(String dingCorpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<DingCorpMappingVo> queryEnterpriseByEA(String ea) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<DingOrderResult> queryLastOrder(String dingCorpId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<List<DingMappingEmployeeResult>> batchGetDingEmps(String ea, List<Integer> empIds) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<EmployeeDingVo> getPersonalData(String appId, String tmpAuthCode) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }

    @Override
    public Result<EmployeeDingVo> queryUserInfoByMe(String authCode, String appId) {
        return Result.newError(ResultCode.FS_CLOUD_NOT_DATA);
    }
}
